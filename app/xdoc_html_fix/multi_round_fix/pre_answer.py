# -*-coding:utf-8 -*-
# Author     ：AI Assistant
# Time       ：2025/6/26
import re
import json
# from typing import List, Dict, Any, Tuple  # 注释掉类型注解以兼容旧版本Python

from app.basic.util import html_util
from app.enums.prompt import GetPrompt
from app.basic.api.doubao import <PERSON><PERSON><PERSON>, DBModel
from app.basic.log import logger


class PreAnswer:
    """
    AI模型答案分析处理模块
    负责对简化后的HTML数据进行AI答案判断和处理
    """

    def __init__(self, subject, task_id):
        self.subject = subject
        self.task_id = task_id

    @staticmethod
    def transform_html_data_for_answer(html_data):
        """
        将HTML数据转换为PreAnswer处理所需的格式

        Args:
            html_data: 原始HTML数据字符串

        Returns:
            tuple: (simplified_html_list, html_elements)
                - simplified_html_list: 简化的HTML列表，格式为 "index:data-label@content"
                - html_elements: 原始HTML元素列表
        """
        # 重新解析HTML数据，准备答案处理
        html_elements = html_util.split_html_v2(html_data)
        simplified_html_list = []

        for index, element in enumerate(html_elements):
            # 只提取 p 标签上的 data-label 属性
            data_label = ""
            if element.strip().startswith('<p'):
                # 提取 p 标签上的 data-label 属性
                p_tag_match = re.search(r'<p[^>]*data-label="([^"]*)"[^>]*>', element)
                data_label = p_tag_match.group(1) if p_tag_match else ""

            # 去掉所有 HTML 标签，保留内容
            content = html_util.del_html_tag(element).strip()

            # 组合成指定格式：index:data-label@content
            simplified_item = f"{index}:{data_label}@{content}"
            simplified_html_list.append(simplified_item)

        return simplified_html_list, html_elements

    async def analyze_simplified_html_list(self, simplified_html_list, html_elements):
        """
        分析简化后的HTML列表，执行AI答案判断并应用结果

        Args:
            simplified_html_list: 简化的HTML列表，格式为 "index:data-label@content"
            html_elements: 原始HTML元素列表

        Returns:
            tuple: (更新后的HTML数据, cost_token)
        """
        cost_token = 0

        # 处理简化后的数据列表
        processed_result = self.process_simplified_list(simplified_html_list)

        # 执行AI判断answer功能
        if processed_result:
            try:
                ai_judgment_result, ai_cost_token = await self.ai_judge_answer(processed_result)
                cost_token += ai_cost_token
                logger.info(f"AI答案判断完成，识别出需要重新标记的内容，消耗token: {ai_cost_token}")

                # 应用AI判断结果到HTML数据
                self._apply_ai_judgment_to_html(ai_judgment_result, simplified_html_list, html_elements)

            except Exception as e:
                logger.error(f"AI答案判断失败: {e}")
                # AI判断失败不影响主流程，继续执行

        # 重新构建HTML数据
        return html_util.join_html(html_elements), cost_token

    def process_simplified_list(self, simplified_html_list):
        """
        处理简化后的HTML列表，按照要求进行分组和拆分

        修改后的答案判断分块逻辑：
        1. 先按照 header 分
        2. header 块中，再按照：按顺序找 answer, 每找到一个 answer，标记为 answer 块的开始，
           继续按顺序找，直到结尾或者找到 explanation 块为止。中间这一部分作为 answer 块
        3. 对 answer 块做分子块，ai 判断等逻辑

        Args:
            simplified_html_list: 格式为 "index:data-label@content" 的列表

        Returns:
            list: 处理后的结果，格式为 [{index: 0, list: [[...], [...]]}, ...]
        """
        # 1. 先按header分大块
        header_groups = self._split_by_header(simplified_html_list)

        # 2. 在每个header组内，按answer分块
        result = []
        result_index = 0

        for header_group in header_groups:
            if header_group:
                # 在header组内按answer分块
                answer_blocks = self._split_by_answer_in_header_group(header_group)

                for answer_block in answer_blocks:
                    if answer_block:
                        # 过滤掉explanation和header标签的行，只保留需要判断的内容
                        filtered_content = self._filter_content_for_judgment(answer_block)

                        # 确保有足够的内容进行AI判断（至少2个项目）
                        if filtered_content and len(filtered_content) >= 2:
                            # 按照固定10个元素进行分割
                            result_lists = []
                            for i in range(0, len(filtered_content), 10):
                                chunk = filtered_content[i:i+10]
                                result_lists.append(chunk)

                            # 如果最后一个块数量不足5，合并到前一个块
                            if len(result_lists) > 1 and len(result_lists[-1]) < 5:
                                last_chunk = result_lists.pop()
                                result_lists[-1].extend(last_chunk)

                            result.append({
                                'index': result_index,
                                'list': result_lists
                            })
                            result_index += 1

        return result

    def _split_by_header(self, simplified_html_list):
        """
        按header标签分割列表
        
        Args:
            simplified_html_list: 完整的简化HTML列表
            
        Returns:
            list: 按header分割的组列表
        """
        groups = []
        current_group = []
        
        for item in simplified_html_list:
            # 解析当前项
            parts = item.split(':', 1)
            if len(parts) < 2:
                current_group.append(item)
                continue
                
            label_content = parts[1]
            label_parts = label_content.split('@', 1)
            if len(label_parts) < 2:
                current_group.append(item)
                continue
                
            data_label = label_parts[0]
            
            # 如果遇到header，开始新的组
            if data_label == 'header':
                if current_group:
                    groups.append(current_group)
                current_group = [item]
            else:
                current_group.append(item)
        
        # 添加最后一个组
        if current_group:
            groups.append(current_group)
            
        return groups

    def _split_by_answer_in_header_group(self, header_group):
        """
        在一个header组内按answer进行分块

        修改后的逻辑：
        1. 找到第一个answer标记，开始answer块
        2. 收集所有后续的answer标记和普通内容，直到遇到explanation或结尾
        3. 将所有连续的answer相关内容作为一个大的answer块

        Args:
            header_group: header组内的项目列表

        Returns:
            list: 按answer分割的块列表
        """
        answer_blocks = []
        current_block = []
        in_answer_section = False

        for item in header_group:
            # 解析当前项
            parts = item.split(':', 1)
            if len(parts) < 2:
                if in_answer_section:
                    current_block.append(item)
                continue

            label_content = parts[1]
            label_parts = label_content.split('@', 1)
            if len(label_parts) < 2:
                if in_answer_section:
                    current_block.append(item)
                continue

            data_label = label_parts[0]

            # 如果遇到answer，进入answer区域（如果还没有进入的话）
            if data_label == 'answer':
                if not in_answer_section:
                    # 第一次遇到answer，开始answer区域
                    in_answer_section = True
                # 无论是否已经在answer区域，都添加这个answer项
                current_block.append(item)

            # 如果遇到explanation，结束answer区域
            elif data_label == 'explanation':
                if in_answer_section and current_block:
                    answer_blocks.append(current_block)
                    current_block = []
                    in_answer_section = False

            # 其他情况
            else:
                if in_answer_section:
                    current_block.append(item)

        # 处理最后一个answer块
        if in_answer_section and current_block:
            answer_blocks.append(current_block)

        return answer_blocks

    def _split_by_explanation_in_group(self, group):
        """
        在一个header组内按explanation进一步分割
        
        Args:
            group: header组内的项目列表
            
        Returns:
            list: 按explanation分割的子组列表
        """
        subgroups = []
        current_subgroup = []
        
        for item in group:
            # 解析当前项
            parts = item.split(':', 1)
            if len(parts) < 2:
                current_subgroup.append(item)
                continue
                
            label_content = parts[1]
            label_parts = label_content.split('@', 1)
            if len(label_parts) < 2:
                current_subgroup.append(item)
                continue
                
            data_label = label_parts[0]
            
            # 如果遇到explanation，结束当前子组并开始新的子组
            if data_label == 'explanation':
                current_subgroup.append(item)
                if current_subgroup:
                    subgroups.append(current_subgroup)
                current_subgroup = []
            else:
                current_subgroup.append(item)
        
        # 添加最后一个子组
        if current_subgroup:
            subgroups.append(current_subgroup)
            
        return subgroups

    def _filter_content_for_judgment(self, content_list):
        """
        过滤内容，移除explanation和header标签的行，只保留需要判断的内容
        
        Args:
            content_list: 内容列表
            
        Returns:
            list: 过滤后的内容列表
        """
        filtered_content = []
        
        for item in content_list:
            # 解析当前项
            parts = item.split(':', 1)
            if len(parts) < 2:
                filtered_content.append(item)
                continue
                
            label_content = parts[1]
            label_parts = label_content.split('@', 1)
            if len(label_parts) < 2:
                filtered_content.append(item)
                continue
                
            data_label = label_parts[0]
            
            # 忽略explanation和header标签的行
            if data_label not in ['explanation', 'header']:
                filtered_content.append(item)
        
        return filtered_content

    async def ai_judge_answer(self, processed_result, model=DBModel.DS_V3.value):
        """
        使用AI判断哪些内容应该被标记为answer

        Args:
            processed_result: process_simplified_list的处理结果
            model: 使用的AI模型

        Returns:
            tuple: (包含原始数据和AI判断结果的字典, cost_token)
        """
        logger.info(f"开始AI判断answer，共{len(processed_result)}个分组")

        ai_results = []
        total_cost_token = 0

        for group_idx, group in enumerate(processed_result):
            logger.info(f"处理分组 {group_idx}，共{len(group['list'])}个answer块")

            group_results = []
            answer_blocks = self._group_answer_blocks(group['list'])

            for block_idx, answer_block in enumerate(answer_blocks):
                logger.info(f"处理answer块 {block_idx}，共{len(answer_block)}个子块")

                block_result, block_cost_token = await self._process_answer_block(answer_block, model)
                total_cost_token += block_cost_token
                if block_result:
                    group_results.append({
                        'block_index': block_idx,
                        'results': block_result
                    })

            if group_results:
                ai_results.append({
                    'group_index': group_idx,
                    'blocks': group_results
                })

        return {
            'original_processed_result': processed_result,
            'ai_judgment_results': ai_results
        }, total_cost_token

    def _group_answer_blocks(self, answer_list):
        """
        将answer块进行分组处理

        Args:
            answer_list: 所有的answer块列表

        Returns:
            list: 分组的块列表
        """
        # 对于答案判断，我们可以简单地将每个块作为一个独立的组
        # 或者根据需要实现更复杂的分组逻辑
        grouped_blocks = []

        for block in answer_list:
            if block:
                grouped_blocks.append([block])

        return grouped_blocks

    async def _process_answer_block(self, answer_block, model):
        """
        处理单个answer块的所有子块

        Args:
            answer_block: answer块的所有子块
            model: AI模型

        Returns:
            tuple: (AI判断结果列表, cost_token)
        """
        results = []
        block_cost_token = 0

        for sub_block_idx, sub_block in enumerate(answer_block):
            logger.info(f"处理子块 {sub_block_idx}")

            # 准备AI判断的输入数据
            input_data = self._prepare_ai_input_for_answer(sub_block)

            try:
                # 调用AI进行判断
                ai_result, ai_cost_token = await self._call_ai_judgment_for_answer(input_data, model)
                block_cost_token += ai_cost_token

                if ai_result:
                    results.append({
                        'sub_block_index': sub_block_idx,
                        'input_data': input_data,
                        'ai_result': ai_result,
                        'original_block': sub_block
                    })

            except Exception as e:
                logger.error(f"AI答案判断失败: {e}")
                # 发生错误时停止处理后续子块
                break

        return results, block_cost_token

    def _prepare_ai_input_for_answer(self, sub_block):
        """
        准备AI判断答案的输入数据，去掉所有label前缀，只保留行号和内容

        Args:
            sub_block: 子块数据

        Returns:
            str: 格式化的输入数据
        """
        input_lines = []

        # 添加当前子块的数据
        for item in sub_block:
            parts = item.split(':', 1)
            if len(parts) >= 2:
                index = parts[0]
                label_content = parts[1]
                label_parts = label_content.split('@', 1)
                if len(label_parts) >= 2:
                    content = label_parts[1]
                    # 只保留行号和内容，去掉所有label前缀
                    input_lines.append(f"{index}:{content}")

        result = '\n'.join(input_lines)
        logger.info(f"准备AI输入数据，共{len(sub_block)}行待判断内容")
        return result

    async def _call_ai_judgment_for_answer(self, input_data, model):
        """
        调用AI进行答案判断

        Args:
            input_data: 输入数据
            model: AI模型

        Returns:
            tuple: (AI判断结果的ID列表, cost_token)
        """
        try:
            # 获取prompt
            prompt = GetPrompt.answer_fix(input_data)

            # 调用AI
            response, tokens = await Doubao.async_chat(prompt, model, temperature=0.1)

            logger.info(f"AI调用成功，使用tokens: {tokens}")

            # 解析JSON响应
            try:
                # 清理响应中的markdown标记
                cleaned_response = self._clean_ai_response(response)
                result = json.loads(cleaned_response)
                if isinstance(result, list):
                    return result, tokens
                else:
                    logger.warning(f"AI返回格式不正确: {response}")
                    return [], tokens
            except json.JSONDecodeError as e:
                logger.error(f"AI返回JSON解析失败: {e}, response: {response}")
                return [], tokens

        except Exception as e:
            logger.error(f"AI调用失败: {e}")
            raise

    def _clean_ai_response(self, response):
        """
        清理AI响应中的markdown标记和其他格式化字符

        Args:
            response: 原始AI响应

        Returns:
            str: 清理后的JSON字符串
        """
        # 去除首尾空白
        cleaned = response.strip()

        # 移除可能的markdown代码块标记
        if cleaned.startswith('```json'):
            cleaned = cleaned[7:]  # 移除 '```json'
        elif cleaned.startswith('```'):
            cleaned = cleaned[3:]   # 移除 '```'

        if cleaned.endswith('```'):
            cleaned = cleaned[:-3]  # 移除结尾的 '```'

        # 再次去除空白
        cleaned = cleaned.strip()

        # 移除可能的其他格式化标记
        lines = cleaned.split('\n')
        json_lines = []
        in_json = False

        for line in lines:
            line = line.strip()
            # 跳过空行和注释行
            if not line or line.startswith('#') or line.startswith('//'):
                continue

            # 检查是否是JSON开始
            if line.startswith('[') or line.startswith('{'):
                in_json = True

            if in_json:
                json_lines.append(line)

            # 检查是否是JSON结束
            if line.endswith(']') or line.endswith('}'):
                break

        # 如果找到了JSON内容，返回拼接的结果
        if json_lines:
            result = '\n'.join(json_lines)
            logger.debug(f"清理后的AI响应: {result}")
            return result

        # 如果没有找到明确的JSON结构，返回原始清理结果
        logger.debug(f"未找到明确JSON结构，返回清理后的响应: {cleaned}")
        return cleaned

    def _apply_ai_judgment_to_html(self, ai_judgment_result, simplified_html_list, html_elements):
        """
        将AI判断结果应用到HTML数据中
        根据AI返回的行号列表，对HTML数据进行处理（添加或移除answer标签）

        Args:
            ai_judgment_result: AI判断结果
            simplified_html_list: 简化的HTML列表
            html_elements: 原始HTML元素列表
        """
        try:
            # 1. 提取所有AI识别为answer的行号
            ai_answer_line_numbers = self._extract_line_numbers_from_ai_result(ai_judgment_result)

            if not ai_answer_line_numbers:
                logger.info("AI判断结果中没有识别出answer内容")
                return

            logger.info(f"AI识别出 {len(ai_answer_line_numbers)} 个行号为answer: {ai_answer_line_numbers}")

            # 2. 获取当前已标记为answer的行号
            current_answer_line_numbers = self._get_current_answer_line_numbers(simplified_html_list)
            logger.info(f"当前已标记为answer的行号: {current_answer_line_numbers}")

            # 3. 计算需要添加和移除answer标签的行号
            lines_to_add = set(ai_answer_line_numbers) - set(current_answer_line_numbers)
            lines_to_remove = set(current_answer_line_numbers) - set(ai_answer_line_numbers)

            logger.info(f"需要添加answer标签的行号: {lines_to_add}")
            logger.info(f"需要移除answer标签的行号: {lines_to_remove}")

            # 4. 更新HTML元素
            updated_count = 0

            # 添加answer标签
            for line_number in lines_to_add:
                if self._update_html_element_answer(html_elements, line_number, add=True):
                    updated_count += 1

            # 移除answer标签
            for line_number in lines_to_remove:
                if self._update_html_element_answer(html_elements, line_number, add=False):
                    updated_count += 1

            logger.info(f"成功更新了 {updated_count} 个HTML元素的data-label属性")

        except Exception as e:
            logger.error(f"应用AI判断结果失败: {e}")
            # 不抛出异常，避免影响主流程

    def _extract_line_numbers_from_ai_result(self, ai_judgment_result):
        """
        从AI判断结果中提取所有被识别为answer的行号

        Args:
            ai_judgment_result: AI判断结果

        Returns:
            list: 被识别为answer的行号列表
        """
        line_numbers = []

        ai_results = ai_judgment_result.get('ai_judgment_results', [])
        for group_result in ai_results:
            for block_result in group_result.get('blocks', []):
                for sub_result in block_result.get('results', []):
                    ai_result = sub_result.get('ai_result', [])
                    if isinstance(ai_result, list):
                        line_numbers.extend(ai_result)

        # 去重并保持顺序
        unique_line_numbers = []
        seen = set()
        for line_number in line_numbers:
            if line_number not in seen:
                unique_line_numbers.append(line_number)
                seen.add(line_number)

        return unique_line_numbers

    def _get_current_answer_line_numbers(self, simplified_html_list):
        """
        获取当前已标记为answer的行号

        Args:
            simplified_html_list: 简化的HTML列表

        Returns:
            list: 当前已标记为answer的行号列表
        """
        answer_line_numbers = []

        for item in simplified_html_list:
            parts = item.split(':', 1)
            if len(parts) >= 2:
                line_number = parts[0]
                label_content = parts[1]
                label_parts = label_content.split('@', 1)
                if len(label_parts) >= 2:
                    data_label = label_parts[0]
                    if data_label == 'answer':
                        answer_line_numbers.append(line_number)

        return answer_line_numbers

    def _update_html_element_answer(self, html_elements, line_number, add):
        """
        更新指定行号的HTML元素的answer标签

        Args:
            html_elements: HTML元素列表
            line_number: 行号
            add: True表示添加answer标签，False表示移除

        Returns:
            bool: 是否成功更新
        """
        try:
            element_index = int(line_number)
            if 0 <= element_index < len(html_elements):
                original_element = html_elements[element_index]

                if add:
                    updated_element = self._add_or_update_answer_label(original_element)
                else:
                    updated_element = self._remove_answer_label(original_element)

                if updated_element != original_element:
                    html_elements[element_index] = updated_element
                    logger.debug(f"{'添加' if add else '移除'}元素 {element_index} 的answer标签")
                    return True
            else:
                logger.warning(f"元素索引 {element_index} 超出范围")
        except ValueError:
            logger.warning(f"无法解析行号: {line_number}")

        return False

    def _remove_answer_label(self, html_element):
        """
        移除HTML元素的data-label="answer"属性

        Args:
            html_element: 原始HTML元素字符串

        Returns:
            str: 移除answer标签后的HTML元素字符串
        """
        # 检查是否是p标签
        if not html_element.strip().startswith('<p'):
            return html_element

        # 使用正则表达式处理p标签
        p_tag_pattern = re.compile(r'<p([^>]*)>')
        match = p_tag_pattern.search(html_element)

        if not match:
            return html_element

        attributes = match.group(1)

        # 移除data-label="answer"属性
        data_label_pattern = re.compile(r'\s*data-label="answer"')
        new_attributes = data_label_pattern.sub('', attributes)

        # 清理多余的空格
        new_attributes = re.sub(r'\s+', ' ', new_attributes).strip()

        # 构建新的p标签
        if new_attributes:
            new_p_tag = f'<p {new_attributes}>'
        else:
            new_p_tag = '<p>'

        # 替换原始HTML中的p标签
        updated_element = p_tag_pattern.sub(new_p_tag, html_element)

        return updated_element

    def _add_or_update_answer_label(self, html_element):
        """
        为HTML元素添加或更新data-label="answer"属性

        Args:
            html_element: 原始HTML元素字符串

        Returns:
            str: 更新后的HTML元素字符串
        """
        # 检查是否是p标签
        if not html_element.strip().startswith('<p'):
            return html_element

        # 使用正则表达式处理p标签
        p_tag_pattern = re.compile(r'<p([^>]*)>')
        match = p_tag_pattern.search(html_element)

        if not match:
            return html_element

        attributes = match.group(1)

        # 检查是否已有data-label属性
        data_label_pattern = re.compile(r'\s*data-label="[^"]*"')

        if data_label_pattern.search(attributes):
            # 替换现有的data-label属性
            new_attributes = data_label_pattern.sub(' data-label="answer"', attributes)
        else:
            # 添加新的data-label属性
            new_attributes = attributes + ' data-label="answer"'

        # 清理多余的空格
        new_attributes = re.sub(r'\s+', ' ', new_attributes).strip()

        # 构建新的p标签
        if new_attributes:
            new_p_tag = f'<p {new_attributes}>'
        else:
            new_p_tag = '<p data-label="answer">'

        # 替换原始HTML中的p标签
        updated_element = p_tag_pattern.sub(new_p_tag, html_element)

        return updated_element
